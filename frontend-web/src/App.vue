<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer v-model="drawer" :rail="rail" permanent @click="rail = false">
      <v-list-item prepend-avatar="/logo.png" :title="$t('app.name')" :subtitle="$t('app.subtitle')" nav>
        <template v-slot:append>
          <v-btn variant="text" icon="mdi-chevron-left" @click.stop="rail = !rail"></v-btn>
        </template>
      </v-list-item>

      <v-divider></v-divider>

      <v-list density="compact" nav>
        <v-list-item v-for="item in navigationItems" :key="item.title" :prepend-icon="item.icon" :title="$t(item.title)"
          :to="item.to" :value="item.value"></v-list-item>
      </v-list>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar>
      <v-app-bar-nav-icon @click="drawer = !drawer"></v-app-bar-nav-icon>

      <v-toolbar-title>{{ pageTitle }}</v-toolbar-title>

      <v-spacer></v-spacer>

      <!-- Language Selector -->
      <v-menu>
        <template v-slot:activator="{ props }">
          <v-btn icon v-bind="props">
            <v-icon>mdi-translate</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item v-for="locale in availableLocales" :key="locale.code" @click="changeLanguage(locale.code)">
            <v-list-item-title>{{ locale.name }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>

      <!-- User Menu -->
      <v-menu>
        <template v-slot:activator="{ props }">
          <v-btn icon v-bind="props">
            <v-avatar size="32">
              <v-img :src="userAvatar" :alt="userName"></v-img>
            </v-avatar>
          </v-btn>
        </template>
        <v-list>
          <v-list-item @click="goToProfile">
            <v-list-item-title>{{ $t('menu.profile') }}</v-list-item-title>
          </v-list-item>
          <v-list-item @click="goToSettings">
            <v-list-item-title>{{ $t('menu.settings') }}</v-list-item-title>
          </v-list-item>
          <v-divider></v-divider>
          <v-list-item @click="logout">
            <v-list-item-title>{{ $t('menu.logout') }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <v-container fluid>
        <!-- Loading Overlay -->
        <v-overlay v-model="isLoading" class="align-center justify-center">
          <LoadingSpinner />
        </v-overlay>

        <!-- Router View -->
        <router-view />
      </v-container>
    </v-main>

    <!-- Footer -->
    <v-footer app>
      <v-spacer></v-spacer>
      <span>&copy; {{ currentYear }} Smart Factory WMS v{{ $version }}</span>
    </v-footer>
  </v-app>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex'

export default {
  name: 'App',

  data() {
    return {
      drawer: true,
      rail: false,
      navigationItems: [
        {
          title: 'menu.dashboard',
          icon: 'mdi-view-dashboard',
          to: '/dashboard',
          value: 'dashboard'
        },
        {
          title: 'menu.inventory',
          icon: 'mdi-package-variant',
          to: '/inventory',
          value: 'inventory'
        },
        {
          title: 'menu.receiving',
          icon: 'mdi-truck-delivery',
          to: '/receiving',
          value: 'receiving'
        },
        {
          title: 'menu.shipment',
          icon: 'mdi-truck',
          to: '/shipment',
          value: 'shipment'
        },
        {
          title: 'menu.production',
          icon: 'mdi-factory',
          to: '/production',
          value: 'production'
        },
        {
          title: 'menu.reports',
          icon: 'mdi-chart-line',
          to: '/reports',
          value: 'reports'
        },
        {
          title: 'menu.users',
          icon: 'mdi-account-group',
          to: '/users',
          value: 'users'
        }
      ],
      availableLocales: [
        { code: 'en', name: 'English' },
        { code: 'ja', name: '日本語' },
        { code: 'zh', name: '中文' },
        { code: 'vi', name: 'Tiếng Việt' }
      ]
    }
  },

  computed: {
    ...mapState('auth', ['user', 'isLoading']),
    ...mapGetters('auth', ['isAuthenticated']),

    pageTitle() {
      return this.$route.meta?.title ? this.$t(this.$route.meta.title) : this.$t('app.name')
    },

    userName() {
      return this.user?.name || this.$t('common.guest')
    },

    userAvatar() {
      return this.user?.avatar || '/default-avatar.png'
    },

    currentYear() {
      return new Date().getFullYear()
    }
  },

  methods: {
    ...mapActions('auth', ['logout']),

    changeLanguage(locale) {
      this.$i18n.locale = locale
      localStorage.setItem('locale', locale)
    },

    goToProfile() {
      this.$router.push('/profile')
    },

    goToSettings() {
      this.$router.push('/settings')
    },

    async logout() {
      try {
        await this.logout()
        this.$router.push('/login')
        this.$q.notify({
          type: 'positive',
          message: this.$t('auth.logout_success'),
          position: 'top'
        })
      } catch (error) {
        this.$q.notify({
          type: 'negative',
          message: this.$t('auth.logout_error'),
          position: 'top'
        })
      }
    }
  },

  created() {
    // Set initial language from localStorage
    const savedLocale = localStorage.getItem('locale')
    if (savedLocale && this.availableLocales.some(l => l.code === savedLocale)) {
      this.$i18n.locale = savedLocale
    }
  }
}
</script>

<style lang="scss">
@import './assets/styles/variables.scss';

.v-application {
  font-family: 'Roboto', sans-serif;
}

.v-main {
  padding-top: 0 !important;
}

.v-container {
  max-width: none;
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
